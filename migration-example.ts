/**
 * FileGator to New Drive Migration Example
 * 
 * This file demonstrates how to use the migration functionality
 * to sync files from an old FileGator drive to the new drive system.
 */

import { FileAPI, SyncProgress } from './new drive/api';

// Example usage of the migration functionality
async function migrateFromOldDrive() {
  // Get the FileAPI instance
  const fileAPI = FileAPI.getInstance();

  // First, authenticate with the new drive
  console.log('🔐 Authenticating with new drive...');
  const { error: loginError } = await fileAPI.login(
    '<EMAIL>',
    'your-new-drive-password'
  );

  if (loginError) {
    console.error('❌ Failed to authenticate with new drive:', loginError);
    return;
  }

  console.log('✅ Successfully authenticated with new drive');

  // Define progress callback to track migration progress
  const onProgress = (progress: SyncProgress) => {
    console.log(`📊 ${progress.stage.toUpperCase()}: ${progress.message} (${Math.round(progress.progress)}%)`);

    if (progress.currentItem) {
      console.log(`   Current: ${progress.currentItem}`);
    }

    if (progress.error) {
      console.log(`   ❌ Error: ${progress.error}`);
    }

    // Show additional info for folder creation stage
    if (progress.stage === 'syncing' && progress.message.includes('Creating folder')) {
      console.log(`   📁 Building folder structure with proper ID mapping...`);
    }
  };

  // Start the migration
  console.log('🚀 Starting migration from FileGator...');
  
  try {
    const result = await fileAPI.syncFromOldDrive(
      'https://your-old-filegator-url.com',  // Old FileGator URL (without trailing slash)
      'your-old-drive-username',              // Old drive username
      'your-old-drive-password',              // Old drive password
      onProgress                              // Progress callback
    );

    if (result.success) {
      console.log('🎉 Migration completed successfully!');
      console.log(`📁 Files migrated: ${result.filesCount}`);
      console.log(`📝 Message: ${result.message}`);
    } else {
      console.error('❌ Migration failed:', result.message);
      if (result.error) {
        console.error('🔍 Error details:', result.error);
      }
    }
  } catch (error) {
    console.error('💥 Unexpected error during migration:', error);
  }
}

// Example of how to stop migration if needed
function setupMigrationCancellation(fileAPI: FileAPI) {
  // Listen for Ctrl+C or other termination signals
  process.on('SIGINT', () => {
    console.log('\n🛑 Cancelling migration...');
    fileAPI.stopOldDriveSyncOperation();
    setTimeout(() => {
      console.log('Migration cancelled.');
      process.exit(0);
    }, 1000);
  });
}

// Example usage with environment variables for automation
async function migrateWithEnvVars() {
  const fileAPI = FileAPI.getInstance();

  // Setup cancellation handler
  setupMigrationCancellation(fileAPI);

  // Get configuration from environment variables
  const config = {
    oldDriveUrl: process.env.OLD_DRIVE_URL,
    oldUsername: process.env.OLD_DRIVE_USERNAME,
    oldPassword: process.env.OLD_DRIVE_PASSWORD,
    newEmail: process.env.NEW_DRIVE_EMAIL,
    newPassword: process.env.NEW_DRIVE_PASSWORD,
  };

  // Validate configuration
  const missingVars = Object.entries(config)
    .filter(([_, value]) => !value)
    .map(([key, _]) => key);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:', missingVars.join(', '));
    console.log('\n💡 Required environment variables:');
    console.log('  OLD_DRIVE_URL - URL of your old FileGator instance');
    console.log('  OLD_DRIVE_USERNAME - Username for old drive');
    console.log('  OLD_DRIVE_PASSWORD - Password for old drive');
    console.log('  NEW_DRIVE_EMAIL - Email for new drive');
    console.log('  NEW_DRIVE_PASSWORD - Password for new drive');
    return;
  }

  try {
    // Authenticate with new drive
    const { error: loginError } = await fileAPI.login(config.newEmail!, config.newPassword!);
    if (loginError) {
      throw new Error(`New drive authentication failed: ${loginError}`);
    }

    // Start migration
    const result = await fileAPI.syncFromOldDrive(
      config.oldDriveUrl!,
      config.oldUsername!,
      config.oldPassword!,
      (progress) => {
        // Simple progress logging
        const bar = '█'.repeat(Math.round(progress.progress / 5)) + 
                   '░'.repeat(20 - Math.round(progress.progress / 5));
        console.log(`[${bar}] ${Math.round(progress.progress)}% - ${progress.message}`);
      }
    );

    if (result.success) {
      console.log(`\n🎉 Migration completed! ${result.filesCount} files migrated.`);
    } else {
      console.error(`\n❌ Migration failed: ${result.message}`);
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Migration error:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Export functions for use in other modules
export {
  migrateFromOldDrive,
  migrateWithEnvVars,
  setupMigrationCancellation
};

// Run migration if this file is executed directly
if (require.main === module) {
  console.log('🎯 FileGator to New Drive Migration');
  console.log('===================================\n');
  
  // Use environment variables if available, otherwise show example
  if (process.env.OLD_DRIVE_URL) {
    migrateWithEnvVars();
  } else {
    console.log('💡 This is an example file. To run migration:');
    console.log('1. Set environment variables (see migrateWithEnvVars function)');
    console.log('2. Or modify the migrateFromOldDrive function with your credentials');
    console.log('3. Then run: node migration-example.js\n');
    
    console.log('📋 Example environment variables:');
    console.log('export OLD_DRIVE_URL="https://your-old-filegator.com"');
    console.log('export OLD_DRIVE_USERNAME="your-username"');
    console.log('export OLD_DRIVE_PASSWORD="your-password"');
    console.log('export NEW_DRIVE_EMAIL="<EMAIL>"');
    console.log('export NEW_DRIVE_PASSWORD="your-new-password"');
  }
}
