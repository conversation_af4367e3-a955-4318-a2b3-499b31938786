<?php
$s3Bucket = '';
$s3Region = 'us-east-1';
$awsAccessKey = '';
$awsSecretKey = '';
return [
    'public_path' => APP_PUBLIC_PATH,
    'public_dir' => APP_PUBLIC_DIR,
    'overwrite_on_upload' => true,
    'timezone' => 'UTC', // https://www.php.net/manual/en/timezones.php
    'download_inline' => ['pdf'], // download inline in the browser, array of extensions, use * for all
    'lockout_attempts' => 5, // max failed login attempts before ip lockout
    'lockout_timeout' => 15, // ip lockout timeout in seconds
    'public_file_base_url' => 'https://' . $s3Bucket . '.s3.' . $s3Region . '.amazonaws.com',

    'webgi' => [
        'version' => '0.8.4',
        'version_x' => '0.2.8',
        'env' => 'https://dist.pixotronics.com/webgi/assets/hdr/gem_2.hdr',
        'script' => '',  // todo
        'script_type' => '',  // like 'module'
        'viewer' => [
//             'version' => '0.8.4',
//             'env' => 'https://dist.pixotronics.com/webgi/assets/hdr/gem_2.hdr',
//             'script' => '',
//             'script_type' => '',
        ],
        'editor' => [
            'presets' => '', // todo
            'vjson' => '', // todo
            'vjson2' => '', // todo
//             'version' => '0.8.4',
//             'env' => 'https://dist.pixotronics.com/webgi/assets/hdr/gem_2.hdr',
//             'script' => '',
//             'script_type' => '',
        ]
    ],

    's3' => [
        'key' => $awsAccessKey,
        'secret' => $awsSecretKey,
        'region' => $s3Region,
        'bucket' => $s3Bucket,
        'version' => 'latest',
        'endpoint' => 'https://s3.' . $s3Region . '.amazonaws.com/' . $s3Bucket,
        'public_viewer_link' => '', // todo
    ],

    'frontend_config' => [
        'app_name' => 'WebGi File Manager',
        'app_version' => APP_VERSION,
        'language' => 'english',
        'logo' => '/img/logo.svg',
        'upload_max_size' => 300 * 1024 * 1024, // 300MB
        'upload_chunk_size' => 5 * 1024 * 1024, // 5MB
        'upload_simultaneous' => 3,
        'default_archive_name' => 'archive.zip',
        'editable' => ['.txt', '.css', '.js', '.ts', '.html', '.php', '.json', '.md'],
        'date_format' => 'YY/MM/DD hh:mm:ss', // see: https://momentjs.com/docs/#/displaying/format/
        'guest_redirection' => '', // useful for external auth adapters
        'search_simultaneous' => 5,
        'filter_entries' => [],
    ],

    'services' => [
        'Filegator\Services\Logger\LoggerInterface' => [
            'handler' => '\Filegator\Services\Logger\Adapters\MonoLogger',
            'config' => [
                'monolog_handlers' => [
                    function () {
                        return new \Monolog\Handler\StreamHandler(
                            __DIR__.'/private/logs/app.log',
                            \Monolog\Logger::DEBUG
                        );
                    },
                ],
            ],
        ],
        'Filegator\Services\Session\SessionStorageInterface' => [
            'handler' => '\Filegator\Services\Session\Adapters\SessionStorage',
            'config' => [
                'handler' => function () {
                    $save_path = null; // use default system path
                    //$save_path = __DIR__.'/private/sessions';
                    $handler = new \Symfony\Component\HttpFoundation\Session\Storage\Handler\NativeFileSessionHandler($save_path);

                    return new \Symfony\Component\HttpFoundation\Session\Storage\NativeSessionStorage([
                        "cookie_samesite" => "Lax",
                        "cookie_secure" => null,
                        "cookie_httponly" => true,
                    ], $handler);
                },
            ],
        ],
        'Filegator\Services\Cors\Cors' => [
            'handler' => '\Filegator\Services\Cors\Cors',
            'config' => [
                'enabled' => true,
            ],
        ],
        'Filegator\Services\Tmpfs\TmpfsInterface' => [
            'handler' => '\Filegator\Services\Tmpfs\Adapters\Tmpfs',
            'config' => [
                'path' => __DIR__.'/private/tmp/',
                'gc_probability_perc' => 10,
                'gc_older_than' => 60 * 60 * 24 * 2, // 2 days
            ],
        ],
        'Filegator\Services\Security\Security' => [
            'handler' => '\Filegator\Services\Security\Security',
            'config' => [
                'csrf_protection' => true,
                'csrf_key' => "123456", // randomize this
                'ip_allowlist' => [],
                'ip_denylist' => [],
                'allow_insecure_overlays' => false,
            ],
        ],
        'Filegator\Services\View\ViewInterface' => [
            'handler' => '\Filegator\Services\View\Adapters\Vuejs',
            'config' => [
                'add_to_head' => '',
                'add_to_body' => '',
            ],
        ],

        'Filegator\Services\Storage\Filesystem' => [
            'handler' => '\Filegator\Services\Storage\Filesystem',
            'config' => [
                'separator' => '/',
                'config' => [],
                'adapter' => function () {
                    $client = new \Aws\S3\S3Client([
                        'credentials' => [
                            'key' => $GLOBALS['awsAccessKey'],
                            'secret' => $GLOBALS['awsSecretKey'],
                        ],
                        'region' => $GLOBALS['s3Region'],
                        'version' => 'latest',
                    ]);

                    return new \League\Flysystem\AwsS3v3\AwsS3Adapter($client, $GLOBALS['s3Bucket']);
                },
            ],
        ],

//        'Filegator\Services\Storage\Filesystem' => [
//            'handler' => '\Filegator\Services\Storage\Filesystem',
//            'config' => [
//                'separator' => '/',
//                'config' => [],
//                'adapter' => function () {
//                    return new \League\Flysystem\Adapter\Local(
//                        __DIR__.'/repository'
//                    );
//                },
//            ],
//        ],
        'Filegator\Services\Archiver\ArchiverInterface' => [
            'handler' => '\Filegator\Services\Archiver\Adapters\ZipArchiver',
            'config' => [],
        ],
        'Filegator\Services\Auth\AuthInterface' => [
            'handler' => '\Filegator\Services\Auth\Adapters\JsonFile',
            'config' => [
                'file' => __DIR__.'/private/users.json',
            ],
        ],
        'Filegator\Services\Router\Router' => [
            'handler' => '\Filegator\Services\Router\Router',
            'config' => [
                'query_param' => 'r',
                'routes_file' => __DIR__.'/backend/Controllers/routes.php',
            ],
        ],
    ],
];
