<!DOCTYPE html>
<html>
<head>
    <title>CORS Test for FileGator</title>
</head>
<body>
    <h1>FileGator CORS Test</h1>
    <button onclick="testCors()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testCors() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';

            try {
                // Test the correct FileGator dev server URL
                const response = await fetch('http://localhost:8081?r=getuser', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    }
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));

                if (response.ok) {
                    const data = await response.text();
                    resultDiv.innerHTML = `
                        <h3>✅ CORS Success!</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Response:</strong></p>
                        <pre>${data}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <h3>⚠️ Response Error</h3>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Status Text:</strong> ${response.statusText}</p>
                    `;
                }
            } catch (error) {
                console.error('CORS Error:', error);
                resultDiv.innerHTML = `
                    <h3>❌ CORS Error</h3>
                    <p><strong>Error:</strong> ${error.message}</p>
                    <p>This usually means CORS is not properly configured.</p>
                    
                    <h4>Troubleshooting:</h4>
                    <ul>
                        <li>Make sure FileGator dev server is running on port 8081</li>
                        <li>Check that CORS is enabled in configuration.php</li>
                        <li>Verify the URL is correct: http://localhost:8081</li>
                        <li>Try using a CORS browser extension</li>
                    </ul>
                `;
            }
        }

        // Auto-test when page loads
        window.addEventListener('load', () => {
            console.log('Page loaded. You can click "Test CORS" to test the connection.');
            console.log('Expected FileGator URL: http://localhost:8081');
        });
    </script>
</body>
</html>
