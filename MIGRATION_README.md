# FileGator to New Drive Migration

This migration system allows you to sync files from an old FileGator drive instance to the new drive system while preserving the complete folder structure and handling errors gracefully.

## Features

- ✅ **Complete file structure preservation** - Maintains all folders and subfolders
- ✅ **Error handling and retry logic** - Automatically retries failed operations
- ✅ **Progress tracking** - Real-time progress updates during migration
- ✅ **Graceful cancellation** - Can be stopped safely at any time
- ✅ **Duplicate detection** - Skips files that already exist
- ✅ **Metadata preservation** - Preserves file timestamps and metadata where possible

## Quick Start

### 1. Basic Usage

```typescript
import { FileAPI } from './new drive/api';

const fileAPI = FileAPI.getInstance();

// Authenticate with new drive
await fileAPI.login('<EMAIL>', 'your-password');

// Start migration
const result = await fileAPI.syncFromOldDrive(
  'https://your-old-filegator.com',  // Old FileGator URL
  'old-username',                    // Old drive username  
  'old-password',                    // Old drive password
  (progress) => {                    // Progress callback
    console.log(`${progress.stage}: ${progress.message} (${progress.progress}%)`);
  }
);

if (result.success) {
  console.log(`Migration completed! ${result.filesCount} files migrated.`);
} else {
  console.error(`Migration failed: ${result.message}`);
}
```

### 2. Using Environment Variables

Set these environment variables for automated migration:

```bash
export OLD_DRIVE_URL="https://your-old-filegator.com"
export OLD_DRIVE_USERNAME="your-username"
export OLD_DRIVE_PASSWORD="your-password"
export NEW_DRIVE_EMAIL="<EMAIL>"
export NEW_DRIVE_PASSWORD="your-new-password"
```

Then run:

```bash
node migration-example.js
```

### 3. Integration in Your App

```typescript
// In your component or service
const handleMigration = async () => {
  const fileAPI = FileAPI.getInstance();
  
  try {
    const result = await fileAPI.syncFromOldDrive(
      oldDriveUrl,
      username,
      password,
      (progress) => {
        // Update your UI with progress
        setMigrationProgress(progress);
      }
    );
    
    if (result.success) {
      showSuccessMessage(`${result.filesCount} files migrated successfully!`);
    } else {
      showErrorMessage(result.message);
    }
  } catch (error) {
    showErrorMessage('Migration failed: ' + error.message);
  }
};

// To cancel migration
const cancelMigration = () => {
  fileAPI.stopOldDriveSyncOperation();
};
```

## API Reference

### `syncFromOldDrive(oldDriveUrl, username, password, progressCallback?)`

Migrates files from an old FileGator instance to the new drive.

**Parameters:**
- `oldDriveUrl` (string): URL of the old FileGator instance
- `username` (string): Username for the old drive
- `password` (string): Password for the old drive  
- `progressCallback` (function, optional): Callback for progress updates

**Returns:**
```typescript
{
  success: boolean;
  message: string;
  filesCount: number;
  error?: string;
}
```

### `stopOldDriveSyncOperation()`

Stops the current migration operation gracefully.

### Progress Callback

The progress callback receives a `SyncProgress` object:

```typescript
{
  stage: 'authenticating' | 'fetching' | 'creating_folder' | 'syncing' | 'complete';
  message: string;
  progress: number;        // 0-100
  currentItem?: string;    // Current file/folder being processed
  error?: string;          // Error message if any
  profile?: {              // Source profile info
    name?: string;
    email?: string;
    avatar?: string;
  };
}
```

## How It Works

1. **Authentication**: Connects to both old and new drive systems
2. **File Discovery**: Recursively scans all files and folders in the old drive
3. **Structure Creation**: Creates the folder structure in the new drive
4. **File Transfer**: Downloads files from old drive and uploads to new drive
5. **Error Handling**: Retries failed operations and continues with remaining files

## File Structure

```
new drive/
├── api.ts              # Main API with syncFromOldDrive method
├── oldDriveClient.ts   # FileGator API client
migration-example.ts    # Usage examples
MIGRATION_README.md     # This documentation
```

## Error Handling

The migration system includes comprehensive error handling:

- **Network errors**: Automatic retry with exponential backoff
- **Authentication failures**: Clear error messages and retry logic
- **File conflicts**: Skips existing files to avoid duplicates
- **Partial failures**: Continues migration even if some files fail
- **Graceful cancellation**: Can be stopped safely without corruption

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify old drive URL is accessible
   - Check username/password are correct
   - Ensure old drive is running FileGator

2. **Files Not Migrating**
   - Check file permissions on old drive
   - Verify network connectivity
   - Look for error messages in progress callback

3. **Migration Stops**
   - Check available storage space on new drive
   - Verify new drive authentication hasn't expired
   - Look for specific error messages

### Debug Mode

Enable detailed logging by setting:

```typescript
// In your progress callback
const onProgress = (progress) => {
  console.log('Progress:', progress);
  if (progress.error) {
    console.error('Error details:', progress.error);
  }
};
```

## Security Notes

- Credentials are only used for the duration of the migration
- No credentials are stored permanently
- All transfers use HTTPS when available
- Session cookies are managed securely

## Support

For issues or questions about the migration system:

1. Check the error messages in the progress callback
2. Verify all credentials and URLs are correct
3. Ensure both old and new drive systems are accessible
4. Check network connectivity and firewall settings
