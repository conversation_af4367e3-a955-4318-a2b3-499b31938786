#!/usr/bin/env node

/**
 * FileGator to New Drive Migration Script
 * 
 * This script migrates files from an old FileGator drive instance to the new drive system.
 * It preserves the complete folder structure and handles errors gracefully.
 * 
 * Usage:
 *   npm run sync-old-drive
 *   or
 *   node sync-old-drive.js
 */

import { FileAP<PERSON>, SyncProgress } from './new drive/api';

interface SyncConfig {
  oldDriveUrl: string;
  username: string;
  password: string;
  newDriveCredentials: {
    email: string;
    password: string;
  };
}

class OldDriveSyncScript {
  private fileAPI: FileAPI;
  private config: SyncConfig | null = null;

  constructor() {
    this.fileAPI = FileAPI.getInstance();
  }

  /**
   * Interactive configuration setup
   */
  async setupConfig(): Promise<SyncConfig> {
    console.log('🔧 FileGator to New Drive Migration Setup');
    console.log('==========================================\n');

    // In a real implementation, you would use a library like 'inquirer' for interactive prompts
    // For now, we'll use environment variables or hardcoded values for demonstration
    
    const config: SyncConfig = {
      oldDriveUrl: process.env.OLD_DRIVE_URL || this.promptForInput('Enter old FileGator drive URL (e.g., https://old-drive.example.com): '),
      username: process.env.OLD_DRIVE_USERNAME || this.promptForInput('Enter old drive username: '),
      password: process.env.OLD_DRIVE_PASSWORD || this.promptForInput('Enter old drive password: ', true),
      newDriveCredentials: {
        email: process.env.NEW_DRIVE_EMAIL || this.promptForInput('Enter new drive email: '),
        password: process.env.NEW_DRIVE_PASSWORD || this.promptForInput('Enter new drive password: ', true)
      }
    };

    // Validate URLs
    if (!this.isValidUrl(config.oldDriveUrl)) {
      throw new Error('Invalid old drive URL provided');
    }

    this.config = config;
    return config;
  }

  /**
   * Simple input prompt (in a real implementation, use inquirer or similar)
   */
  private promptForInput(message: string, isPassword: boolean = false): string {
    // This is a placeholder - in a real implementation you'd use readline or inquirer
    console.log(message);
    if (isPassword) {
      console.log('(Password input would be hidden in real implementation)');
    }
    
    // For demo purposes, return environment variables or throw error
    throw new Error('Interactive input not implemented - please use environment variables');
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Authenticate with new drive
   */
  async authenticateNewDrive(): Promise<void> {
    if (!this.config) {
      throw new Error('Configuration not set up');
    }

    console.log('🔐 Authenticating with new drive...');
    
    const { error } = await this.fileAPI.login(
      this.config.newDriveCredentials.email,
      this.config.newDriveCredentials.password
    );

    if (error) {
      throw new Error(`Failed to authenticate with new drive: ${error}`);
    }

    console.log('✅ Successfully authenticated with new drive');
  }

  /**
   * Progress callback for sync operation
   */
  private onSyncProgress = (progress: SyncProgress): void => {
    const progressBar = this.createProgressBar(progress.progress);
    
    console.clear();
    console.log('📁 FileGator Migration in Progress');
    console.log('==================================\n');
    
    console.log(`Stage: ${progress.stage.toUpperCase()}`);
    console.log(`${progressBar} ${Math.round(progress.progress)}%`);
    console.log(`Status: ${progress.message}`);
    
    if (progress.currentItem) {
      console.log(`Current: ${progress.currentItem}`);
    }
    
    if (progress.profile) {
      console.log(`\nSource Profile:`);
      console.log(`  Name: ${progress.profile.name || 'N/A'}`);
      console.log(`  Email: ${progress.profile.email || 'N/A'}`);
    }
    
    if (progress.error) {
      console.log(`\n❌ Error: ${progress.error}`);
    }
    
    console.log('\nPress Ctrl+C to cancel migration');
  };

  /**
   * Create a simple progress bar
   */
  private createProgressBar(progress: number, width: number = 30): string {
    const filled = Math.round((progress / 100) * width);
    const empty = width - filled;
    return `[${'█'.repeat(filled)}${' '.repeat(empty)}]`;
  }

  /**
   * Run the migration
   */
  async runMigration(): Promise<void> {
    if (!this.config) {
      throw new Error('Configuration not set up');
    }

    console.log('🚀 Starting migration from FileGator to New Drive...\n');

    try {
      const result = await this.fileAPI.syncFromOldDrive(
        this.config.oldDriveUrl,
        this.config.username,
        this.config.password,
        this.onSyncProgress
      );

      console.clear();
      console.log('📁 FileGator Migration Complete');
      console.log('===============================\n');

      if (result.success) {
        console.log('✅ Migration completed successfully!');
        console.log(`📊 Files migrated: ${result.filesCount}`);
        console.log(`📝 ${result.message}`);
      } else {
        console.log('❌ Migration failed:');
        console.log(`📝 ${result.message}`);
        if (result.error) {
          console.log(`🔍 Error details: ${result.error}`);
        }
        process.exit(1);
      }
    } catch (error) {
      console.clear();
      console.log('❌ Migration Error');
      console.log('==================\n');
      console.error('An unexpected error occurred:', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown(): void {
    const shutdown = () => {
      console.log('\n\n🛑 Migration cancelled by user');
      console.log('Stopping sync operation...');
      
      this.fileAPI.stopOldDriveSyncOperation();
      
      setTimeout(() => {
        console.log('Migration stopped.');
        process.exit(0);
      }, 1000);
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
  }

  /**
   * Main execution function
   */
  async run(): Promise<void> {
    try {
      this.setupGracefulShutdown();
      
      console.log('🎯 FileGator to New Drive Migration Tool');
      console.log('========================================\n');
      
      // Setup configuration
      await this.setupConfig();
      
      // Authenticate with new drive
      await this.authenticateNewDrive();
      
      // Run migration
      await this.runMigration();
      
    } catch (error) {
      console.error('\n❌ Setup Error:', error instanceof Error ? error.message : String(error));
      console.log('\n💡 Tips:');
      console.log('  - Make sure all URLs are accessible');
      console.log('  - Verify your credentials are correct');
      console.log('  - Check your network connection');
      console.log('  - Use environment variables for automated runs:');
      console.log('    OLD_DRIVE_URL, OLD_DRIVE_USERNAME, OLD_DRIVE_PASSWORD');
      console.log('    NEW_DRIVE_EMAIL, NEW_DRIVE_PASSWORD');
      process.exit(1);
    }
  }
}

// Export for use as module
export { OldDriveSyncScript, SyncConfig };

// Run if called directly
if (require.main === module) {
  const syncScript = new OldDriveSyncScript();
  syncScript.run();
}
