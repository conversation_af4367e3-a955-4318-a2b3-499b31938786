export interface OldDriveFile {
  name: string;
  path: string;
  type: 'file' | 'dir';
  size?: number;
  timestamp?: number;
}

export interface OldDriveAuthResult {
  success: boolean;
  user?: any;
  error?: string;
}

export class OldDriveClient {
  private baseUrl: string;
  private sessionCookie: string | null = null;
  private isAuthenticated: boolean = false;
  private maxRetries: number = 3;
  private retryDelay: number = 1000;
  private csrfToken: string = '';
  private debug: boolean = false;

  constructor(baseUrl: string, debug: boolean = false) {
    this.baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    this.debug = debug;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private log(message: string, ...args: any[]) {
    if (this.debug) {
      console.log(`[OldDriveClient] ${message}`, ...args);
    }
  }

  private async retryOperation<T>(
    operation: () => Promise<T>,
    operationName: string,
    retries: number = this.maxRetries
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        this.log(`${operationName} attempt ${attempt}/${retries} failed:`, lastError.message);

        if (attempt < retries) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw new Error(`${operationName} failed after ${retries} attempts: ${lastError?.message}`);
  }

  async authenticate(username: string, password: string): Promise<OldDriveAuthResult> {

    return this.retryOperation(async () => {
      const userResponse = await fetch(`${this.baseUrl}?r=/getuser`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });


      const csrfToken = userResponse.headers.get('x-csrf-token');
      if (csrfToken) {
        this.csrfToken = csrfToken;
      }

      const setCookieHeader = userResponse.headers.get('set-cookie');
      if (setCookieHeader) {
        const sessionMatch = setCookieHeader.match(/filegator=([^;]+)/);
        if (sessionMatch) {
          this.sessionCookie = sessionMatch[1];
        }
      }

      if (userResponse.ok) {
        const userData = await userResponse.json();

        // if we get user data and it's not a guest, we're already authenticated
        if (userData.data && userData.data.username && userData.data.role !== 'guest') {
          this.isAuthenticated = true;
          return {
            success: true,
            user: userData.data
          };
        }
      }

      const loginResponse = await fetch(`${this.baseUrl}?r=/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': this.csrfToken,
          'Cookie': this.sessionCookie ? `filegator=${this.sessionCookie}` : ''
        },
        body: JSON.stringify({ username, password }),
        credentials: 'include'
      });

      const newCsrfToken = loginResponse.headers.get('x-csrf-token');
      if (newCsrfToken) {
        this.csrfToken = newCsrfToken;
      }

      if (!loginResponse.ok) {
        const errorText = await loginResponse.text();
        throw new Error(`Authentication failed: ${loginResponse.status} ${loginResponse.statusText} - ${errorText}`);
      }

      const responseData = await loginResponse.json();

      if (responseData.data) {
        this.isAuthenticated = true;

        const newSetCookieHeader = loginResponse.headers.get('set-cookie');
        if (newSetCookieHeader) {
          const newSessionMatch = newSetCookieHeader.match(/filegator=([^;]+)/);
          if (newSessionMatch) {
            this.sessionCookie = newSessionMatch[1];
          }
        }

        return {
          success: true,
          user: responseData.data
        };
      } else {
        throw new Error('Login failed: ' + (responseData.message || 'Invalid credentials'));
      }
    }, 'Authentication').catch(error => ({
      success: false,
      error: error.message
    }));
  }

  async getDirectory(path: string = '/'): Promise<{ success: boolean; files?: OldDriveFile[]; error?: string }> {
    if (!this.isAuthenticated) {
      return { success: false, error: 'Not authenticated' };
    }

    return this.retryOperation(async () => {
      // Refresh CSRF token if needed (like frontend does)
      if (!this.csrfToken) {
        const csrfResponse = await fetch(`${this.baseUrl}?r=getuser`, {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Cookie': this.sessionCookie ? `filegator=${this.sessionCookie}` : ''
          }
        });

        const newCsrfToken = csrfResponse.headers.get('x-csrf-token');
        if (newCsrfToken) {
          this.csrfToken = newCsrfToken;
        }
      }

      // Make the directory listing request
      const response = await fetch(`${this.baseUrl}?r=/getdir`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-CSRF-Token': this.csrfToken,
          'Cookie': this.sessionCookie ? `filegator=${this.sessionCookie}` : ''
        },
        body: JSON.stringify({ dir: path }),
        credentials: 'include'
      });

      // Update CSRF token from response (like frontend does)
      const newCsrfToken = response.headers.get('x-csrf-token');
      if (newCsrfToken) {
        this.csrfToken = newCsrfToken;
      }

      if (!response.ok) {
        throw new Error('Failed to get directory: ' + response.statusText);
      }

      const responseData = await response.json();

      const files: OldDriveFile[] = responseData.data?.files?.map((file: any) => ({
        name: file.name,
        path: file.path,
        type: file.type === 'dir' ? 'dir' : 'file',
        size: file.size || 0,
        timestamp: file.timestamp || 0
      })) || [];

      return {
        success: true,
        files: files.filter(f => f.name !== '..') // remove parent directory entry
      };
    }, `Get directory ${path}`).catch(error => ({
      success: false,
      error: error.message
    }));
  }

  async getAllFiles(rootPath: string = '/'): Promise<{ success: boolean; files?: OldDriveFile[]; error?: string }> {
    const allFiles: OldDriveFile[] = [];
    const processedPaths = new Set<string>();

    const processDirectory = async (dirPath: string): Promise<void> => {
      if (processedPaths.has(dirPath)) return;
      processedPaths.add(dirPath);

      const result = await this.getDirectory(dirPath);
      if (!result.success || !result.files) {
        throw new Error(result.error || 'Failed to get directory');
      }

      for (const file of result.files) {
        allFiles.push(file);
        
        if (file.type === 'dir') {
          await processDirectory(file.path);
        }
      }
    };

    try {
      await processDirectory(rootPath);
      return {
        success: true,
        files: allFiles
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  private async getConfig(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}?r=/getconfig`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cookie': this.sessionCookie ? `filegator=${this.sessionCookie}` : ''
        }
      });

      if (response.ok) {
        const configData = await response.json();
        return configData.data || {};
      }
    } catch (error) {
      this.log('⚠️ Failed to get config:', error);
    }
    return {};
  }

  private getDownloadLink(filePath: string, config: any): string {
    // Use the same logic as frontend: check for public_file_base_url first
    const publicFileBaseUrl = config.public_file_base_url;
    if (publicFileBaseUrl) {
      this.log('📁 Using public_file_base_url:', publicFileBaseUrl);
      return publicFileBaseUrl + filePath;
    }

    // Fallback to FileGator download endpoint
    const encodedPath = btoa(filePath);
    return `${this.baseUrl}?r=/download&path=${encodedPath}`;
  }

  async downloadFile(filePath: string): Promise<{ success: boolean; blob?: Blob; error?: string }> {
    if (!this.isAuthenticated) {
      return { success: false, error: 'Not authenticated' };
    }

    return this.retryOperation(async () => {
      // Get config to check for public_file_base_url (like frontend does)
      const config = await this.getConfig();
      const downloadUrl = this.getDownloadLink(filePath, config);

      this.log('📥 Downloading from:', downloadUrl);

      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Cookie': this.sessionCookie ? `filegator=${this.sessionCookie}` : ''
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to download file: ' + response.statusText);
      }

      const blob = await response.blob();
      return {
        success: true,
        blob
      };
    }, `Download file ${filePath}`).catch(error => ({
      success: false,
      error: error.message
    }));
  }
}

